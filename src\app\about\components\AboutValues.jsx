"use client";

import React from "react";
import Image from "next/image";
import { images } from "@/constants";

const AboutValues = () => {
  return (
    <div className="app__aboutpage-values">
      <h2 className="headtext__cormorant">Our Philosophy</h2>
      <div className="app__aboutpage-values_spoon">
        <Image src={images.spoon} alt="spoon" width={45} height={15} />
      </div>
      <div className="app__aboutpage-values_cards">
        <div className="app__aboutpage-values_card">
          <h3 className="p__cormorant">Exceptional Ingredients</h3>
          <p className="p__opensans">
            We source only the finest seasonal ingredients from local farmers and premium suppliers around the world, ensuring each dish represents the peak of flavor and quality.
          </p>
        </div>
        <div className="app__aboutpage-values_card">
          <h3 className="p__cormorant">Culinary Artistry</h3>
          <p className="p__opensans">
            Our expert chefs blend traditional techniques with innovative approaches, creating dishes that are both familiar and surprising—a true celebration of culinary creativity.
          </p>
        </div>
        <div className="app__aboutpage-values_card">
          <h3 className="p__cormorant">Elegant Ambiance</h3>
          <p className="p__opensans">
            Every element of our space has been thoughtfully designed to create an atmosphere of refined luxury that complements our culinary offerings and enhances your dining experience.
          </p>
        </div>
        <div className="app__aboutpage-values_card">
          <h3 className="p__cormorant">Impeccable Service</h3>
          <p className="p__opensans">
            Our dedicated team is committed to anticipating your needs and providing attentive, personalized service that makes every visit to BLUBRASSERIE truly memorable.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AboutValues;
