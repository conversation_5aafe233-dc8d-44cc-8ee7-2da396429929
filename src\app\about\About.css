.app__aboutpage {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: rgba(15, 40, 70, 0.95);
}

.app__aboutpage-heading {
  text-align: center;
  margin-bottom: 4rem;
}

.app__aboutpage-back-link {
  margin-top: 1rem;
}

.app__aboutpage-back-link a {
  color: var(--color-golden);
  transition: color 0.3s ease;
  display: inline-flex;
  align-items: center;
}

.app__aboutpage-back-link a:hover {
  color: var(--color-white);
}

.app__aboutpage-content {
  display: flex;
  flex-direction: column;
  gap: 6rem;
}

/* History Section */
.app__aboutpage-history {
  display: flex;
  gap: 4rem;
  align-items: center;
}

.app__aboutpage-history_image {
  flex: 1;
  position: relative;
  min-width: 300px;
}

.app__aboutpage-history_overlay {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 0;
  opacity: 0.2;
}

.app__aboutpage-history_image img:nth-child(2) {
  width: 100%;
  height: auto;
  border-radius: 4px;
  position: relative;
  z-index: 1;
  opacity: 0.9;
}

.app__aboutpage-history_text {
  flex: 1.2;
}

.app__aboutpage-history_text h2,
.app__aboutpage-values h2,
.app__aboutpage-team h2,
.app__aboutpage-visit h2 {
  color: var(--color-golden);
  margin-bottom: 1rem;
}

.app__aboutpage-history_spoon,
.app__aboutpage-values_spoon,
.app__aboutpage-team_spoon,
.app__aboutpage-visit_spoon {
  margin-bottom: 2rem;
}

.app__aboutpage-history_text p {
  margin: 1.5rem 0;
  color: var(--color-grey);
  line-height: 1.8;
}

/* Values Section */
.app__aboutpage-values {
  text-align: center;
}

.app__aboutpage-values_cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.app__aboutpage-values_card {
  background: rgba(15, 40, 70, 0.7);
  padding: 2rem;
  border: 1px solid var(--color-golden);
  border-radius: 8px;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.app__aboutpage-values_card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--color-golden), transparent);
  transition: left 0.6s ease;
}

.app__aboutpage-values_card:hover::before {
  left: 100%;
}

.app__aboutpage-values_card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(220, 202, 135, 0.15), 0 5px 15px rgba(0, 0, 0, 0.3);
  background: rgba(15, 40, 70, 0.9);
  border-color: rgba(220, 202, 135, 0.8);
}

.app__aboutpage-values_card h3 {
  color: var(--color-golden);
  margin-bottom: 1.2rem;
  font-size: 24px;
  position: relative;
  padding-bottom: 0.5rem;
}

.app__aboutpage-values_card h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 1px;
  background: var(--color-golden);
  opacity: 0.6;
}

.app__aboutpage-values_card p {
  color: var(--color-grey);
  line-height: 1.8;
  font-size: 15px;
  text-align: justify;
}

/* Team Section */
.app__aboutpage-team {
  text-align: center;
}

.app__aboutpage-team_content {
  display: flex;
  gap: 4rem;
  align-items: center;
  margin-top: 2rem;
}

.app__aboutpage-team_image {
  flex: 1;
  min-width: 280px;
}

.app__aboutpage-team_image img {
  width: 100%;
  height: auto;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.app__aboutpage-team_text {
  flex: 1.5;
  text-align: left;
}

.app__aboutpage-team_text h3 {
  font-size: 32px;
  margin-bottom: 0.5rem;
}

.app__aboutpage-team_text h4 {
  margin-bottom: 1.5rem;
  font-size: 20px;
}

.app__aboutpage-team_text p {
  margin-bottom: 1.5rem;
  color: var(--color-grey);
  line-height: 1.8;
}

/* Visit Section */
.app__aboutpage-visit {
  text-align: center;
}

.app__aboutpage-visit_content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 2rem;
}

.app__aboutpage-visit_info {
  margin-bottom: 3rem;
}

.app__aboutpage-visit_info h3 {
  color: var(--color-golden);
  margin: 2rem 0 1rem 0;
  font-size: 23px;
}

.app__aboutpage-visit_info h3:first-child {
  margin-top: 0;
}

.app__aboutpage-visit_info p {
  color: var(--color-grey);
  margin: 0.5rem 0;
}

.app__aboutpage-visit_buttons {
  display: flex;
  gap: 2rem;
}

.app__aboutpage-visit_buttons .custom__button {
  background-color: var(--color-crimson);
  color: var(--color-black);
  padding: 0.75rem 2rem;
  border-radius: 1px;
  transition: all 0.3s ease;
  border: 1px solid var(--color-golden);
  cursor: pointer;
  font-weight: 700;
  letter-spacing: 0.04em;
  line-height: 28px;
  font-size: 16px;
}

.app__aboutpage-visit_buttons .custom__button:hover {
  background-color: transparent;
  color: var(--color-golden);
}

/* Responsive Styles */
@media screen and (max-width: 1150px) {
  .app__aboutpage-history,
  .app__aboutpage-team_content {
    flex-direction: column;
    gap: 3rem;
  }
  
  .app__aboutpage-history_text,
  .app__aboutpage-team_text {
    text-align: center;
  }
  
  .app__aboutpage-history_image,
  .app__aboutpage-team_image {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
  }
}

@media screen and (max-width: 850px) {
  .app__aboutpage-values_cards {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
  
  .app__aboutpage-visit_buttons {
    flex-direction: column;
    gap: 1rem;
  }
}

@media screen and (max-width: 650px) {
  .app__aboutpage-content {
    gap: 4rem;
  }
  
  .app__aboutpage-values_cards {
    grid-template-columns: 1fr;
  }
}

.app__bg {
  background: url('/assets/bgwhiteblue.png');
  background-position: center;
  background-size: cover;
  background-repeat: repeat;
  background-attachment: fixed;
  background-color: var(--color-black);
} 